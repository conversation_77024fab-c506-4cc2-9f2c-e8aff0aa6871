.plans-modal {
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.plans-header {
  background-image: url("/assets/images/labtop.png");
  height: 100%;
  background-repeat: round;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.header-content {
  padding: 30px 24px;
  display: flex;
  flex-direction: column;
  height: 19rem;
}

.support-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
}
.text-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  color: #722282;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
}
.hero-image {
  display: flex;
  position: absolute;
  background-color: white;
  justify-content: center;
}

.plans-body {
  padding: 24px;
  background-color: white;
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
