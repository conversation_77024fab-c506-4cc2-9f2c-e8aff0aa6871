<div class="ad-card" [ngClass]="{ selected: isSelected && isSelectedMode }" *ngIf="listingDetails">
    <div class="ad-card-content">
        <div class="ad-card-right">
            <label class="custom-checkbox" *ngIf="isSelectedMode">
                <input type="checkbox" [checked]="isSelected" />
                <span class="checkmark"></span>
            </label>

            <img [src]="listingDetails.webpImageURL" alt="Ad Image" />
        </div>

        <div class="ad-card-left" (click)="goToListingDetials()">
            <div class="ad-card-left-item">
                <div class="ad-header">
                    <h3 class="ad-title">{{ listingDetails.name }}</h3>
                </div>

                <app-svg-icons *ngIf="!isSelectedMode" name="back-icon" width="12px" height="12px"></app-svg-icons>
            </div>
            <div class="ad-card-left-item">
                <div class="metrics">
                    <div class="metric">
                        <div>
                            <app-svg-icons name="eye-icon" width="15px" height="15px"></app-svg-icons>
                            <span class="value">{{ listingDetails.appearanceCount ?? 0}}</span>
                        </div>
                        <span class="label">الظهـور</span>
                    </div>
                    <div class="metric">
                        <div>
                            <app-svg-icons name="arrow-top-left" width="15px" height="15px"></app-svg-icons>
                            <span class="value">{{ listingDetails.views ?? 0}}</span>
                        </div>
                        <span class="label">{{'views' | translate }}</span>
                    </div>
                    <div class="metric">
                        <div>
                            <app-svg-icons name="phone-icon" width="15px" height="15px"></app-svg-icons>
                            <span class="value">{{ listingDetails.callCount ?? 0}}</span>
                        </div>
                        <span class="label">المكالمات</span>
                    </div>
                </div>

            </div>
            <div class="tags">
                <div class="tag-label" *ngFor="let tag of listingDetails.tags">
                    <app-svg-icons name="featured-icon" width="13px" height="13px"></app-svg-icons>
                    <span>{{ tag }}</span>
                </div>
            </div>
        </div>

    </div>

    <app-shared-btn *ngIf="!isSelectedMode" label="الاضافة" [iconName]="'icon-circle-plus'" [iconWidth]="'10px'"
        [iconHeight]="'10px'" [size]="'small'" [theme]="'bgWhite'" (btnClick)="addSelectedAds()"></app-shared-btn>

</div>