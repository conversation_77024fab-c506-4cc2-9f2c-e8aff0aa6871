import { CommonModule } from '@angular/common';
import { Component, Input} from '@angular/core';
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";
import { PointsStatusWidgetComponent } from "@src/app/shared/components/points-status-widget/points-status-widget.component";
import { SvgIconsComponent } from '@src/app/shared/components/svg-icons/svg-icons.component';
@Component({
  selector: 'app-points',
  standalone: true,
  imports: [CommonModule, DynamicHeaderComponent, PointsStatusWidgetComponent, SvgIconsComponent],
  templateUrl: './points.component.html',
  styleUrl: './points.component.scss'
})
export class PointsComponent {
  pointsData: any[] = [1]; 
  @Input() price: string = "2,340";
  @Input() currency: string = "EGP";
  @Input() label: string = "نقاط الباقة الاساسية";
  @Input() expiresAt: string =  "11 فبراير 2025" ;
  @Input() pointsText: string = "+2,340 نقطة"
  @Input() buyBtnText: string = "شراء";
  @Input() dateLabel: string = " تاريخ الانتهاء :";
  @Input() buyBtnBg: string = ""; 
  @Input() buyBtnTextColor: string = ""; 
}
