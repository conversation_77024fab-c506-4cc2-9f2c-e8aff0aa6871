<div class="plan-content">
    <div class="price-section">
      <div class="price">
        <span class="price-value">{{ plan?.price }}</span>
        <span class="price-unit">جنيه/شهريًا</span>
      </div>
      <div class="plan-title">{{ plan?.name }}</div>
    </div>
        <div class="plan-html" [innerHTML]="plan?.htmlDescription || ''"></div>
        
        <ng-container *ngIf="plan?.isCurrentPackage; else notCurrent">
            <div class="styling-options">
                <div class="renewal-text">
                    <span class="label">تجدد تلقائياً في الإعلان في </span>
                    <span class="date">{{plan?.packageEndDate | fromNow }}</span>
                </div>
            </div>
        </ng-container>
        
        <ng-template #notCurrent>
            <div class="renewal-note" *ngIf="plan?.renewableLabel">
                <span class="note-icon">
                    <app-svg-icons name="info-icon" width="12px" height="12px"></app-svg-icons>
                </span>
                <span class="note-text">{{ plan?.renewableLabel }} {{plan?.packageValidDays}}</span>
            </div>
        </ng-template>
</div>
