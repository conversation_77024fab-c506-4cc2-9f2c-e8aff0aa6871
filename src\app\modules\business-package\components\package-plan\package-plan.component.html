<div class="plan-container">
  <div class="plan-card">
    <div class="badge-wrapper" *ngIf="plan?.labelTag">
      <div class="badge">{{ plan?.labelTag }}</div>
    </div>

    <app-package-details [plan]="plan"></app-package-details>

    <ng-container *ngIf="!isSubscribed; else hasEndDate">
      <app-shared-btn [label]="'اشترك'" [size]="'medium'" [bgcolor]="'#722282'" [labelColor]="'#fff'"
        (click)="selectPlan()"></app-shared-btn>
    </ng-container>

    <ng-template #hasEndDate>
      <ng-container *ngIf="plan?.isCurrentPackage; else changePackage">
        <app-shared-btn [label]="'الغاء تجديد الباقة'" [size]="'medium'" [bgcolor]="'none'" [labelColor]="'#722282'"
          (click)="unsubscrip()"></app-shared-btn>
      </ng-container>

      <ng-template #changePackage>
        <app-shared-btn [label]="'تغيير الباقة'" [size]="'medium'" [bgcolor]="'#722282'" [labelColor]="'#fff'"
          (click)="selectPlan()"></app-shared-btn>
      </ng-template>
    </ng-template>
  </div>
</div>