<div class="plans-modal">
  <div class="plans-header">
    <div class="header-content">
      <div class="support-btn">
        <button class="text-btn">
          <app-svg-icons name="icon-phone-alt" width="18px" height="18px"></app-svg-icons>
          اتصل بالدعم
        </button>
        <app-svg-icons name="icon-close-big" width="21px" height="21px" (click)="close()"></app-svg-icons>
      </div>

      <div class="hero-image"></div>
    </div>

    <div class="plans-contain">
      <div class="plans-body">
        <ng-container *ngIf="plans?.length">
          <div class="plan-grid">
            <app-package-plan *ngFor="let p of plans" [plan]="p" [isSubscribed]="isSubscribed()"
              (click)="select(p.id)"></app-package-plan>
          </div>
        </ng-container>
        <app-custom-package-plan duration="90 يومًا" adCount="حسب الطلب" availablePoints="حسب الطلب" [features]="[
            'جميع ميزات Power Seller',
            'تكامل API',
            'تحليلات تفصيلية من مركز التاجر',
            'تصدير بيانات التفاعل المرتبطة بعملاء محددين'
          ]" support="اتفاقية مستوى خدمة خاصة (SLA)" price="حسب العرض (Quotation)"
          renewalNote="سيتم تجديد تلقائًا بعد 90 يوم" (click)="goToCustom()"></app-custom-package-plan>

        <app-shared-btn [label]="'تحويل الي حساب فرد'" [size]="'medium'" [bgcolor]="'#fff'" [labelColor]="'#722282'"
          [boxShadow]="'#72228233'" [iconName]="'icon-user'" [iconWidth]="'18px'" [iconHeight]="'18px'"
          (click)="goToCancel()"></app-shared-btn>
      </div>
    </div>
  </div>
</div>