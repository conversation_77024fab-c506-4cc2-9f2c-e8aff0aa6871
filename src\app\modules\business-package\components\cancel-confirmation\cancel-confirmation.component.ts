import { Component, Input } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";
import { ViewSwitchService } from "../../services/view-switch.service";
import { DynamicHeaderComponent } from "@src/app/shared/components/dynamic-header/dynamic-header.component";

@Component({
  selector: "app-cancel-confirmation",
  standalone: true,
  imports: [SharedBtnComponent, DynamicHeaderComponent],
  templateUrl: "./cancel-confirmation.component.html",
  styleUrl: "./cancel-confirmation.component.scss",
})
export class CancelConfirmationComponent {
  @Input({ required: true }) titleText: string = "";
  @Input() cancelDate: string = "";
  @Input() cancelDateValue: string | null = "";
  @Input() btnText: string = "";
  constructor(private view: ViewSwitchService) {}
  
  goback(){
    this.view.go("business");
  }

}
