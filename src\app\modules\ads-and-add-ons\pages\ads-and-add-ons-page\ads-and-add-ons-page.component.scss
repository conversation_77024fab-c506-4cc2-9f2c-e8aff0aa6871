@import "variables";
@import "mixins";

.dashboard-page {
    direction: inherit;
    padding: 16px;
    background: white;
    min-height: 100vh;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.ads-stats {
    color: rgba(39, 39, 40, 1);
}

.grouped-ads {
    cursor: pointer;
    color: $primary;
    font-weight: bold;
    font-size: 12px;
    padding-bottom: 4px;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.action-buttons,
.quick-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0px;
}

.quick-buttons {
    flex-wrap: wrap;
}

.add-button {
    padding: 0.2rem 0.6rem;
    border-radius: 6px;
    border: 1px solid rgba(114, 34, 130, 0.2);
    background-color: white;
    color: rgba(114, 34, 130, 1);
    font-weight: 600;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;

}

.delete {
    background-color: rgba(114, 34, 130, 0.1) !important;
}

.cancel-button {
    background: none;
    border: none;
    color: #683b90;
    font-size: 10px;
    font-weight: 700;
    cursor: pointer;
    padding: 0;
}

.action-button button {
    background-color: white;
    border: 0.5px solid rgba($primary, 0.2);
    color: $primary-dark;
    border-radius: 6px;
    padding: 5px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    width: 100%;
    text-align: center;
}

.action-button .plus {
    font-weight: bold;
}

.quick-buttons button {
    padding: 0.2rem 0.6rem;
    border-radius: 6px;
    border: 1px solid rgba(114, 34, 130, 0.2);
    background-color: white;
    color: rgba(114, 34, 130, 1);
    font-weight: 00;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;

    .delete {
        background-color: rgba(114, 34, 130, 0.1);
    }
}