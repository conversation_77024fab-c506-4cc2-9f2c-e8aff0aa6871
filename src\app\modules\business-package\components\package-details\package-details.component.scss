.plan-content {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}
.price-section {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}
.price {
  color: #722282; 
  font-weight: 800;
  font-size: 24px;
  line-height: 28px;
}

.price-unit {
  font-size: 12px;
  font-weight: 600;
  margin-right: 4px;
}

.plan-title {
  color: #722282;
  font-size: 14px;
  font-weight: 700;
}
.package-details {
  color: #272728;
  font-size: 14px;
  padding: 0px;
  line-height: 25px;
}


.label {
  font-weight: 700;
}

.styling-options{
  width: 100%;
  padding: 12px;
  background: #D9D9D9;
  border-radius: 6px;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;        
}

.renewal-text{
  align-self: stretch;
  text-wrap: balance;
}

.renewal-text .label{
  color: #272728;
  font-size: 12px;
  font-weight: 600;
  line-height: 15.5px;
  word-wrap: break-word;
}

.renewal-text .date{
  color: #272728;
  font-size: 12px;
  font-weight: 700;
  line-height: 15.5px;
  word-wrap: break-word;
  
  .renewal-note {
    width: 100%;
    display: inline-flex;
    justify-content: flex-start;
    align-items: baseline;
    gap: 6px;
    margin-top: 12px;
  
    .note-text {
      font-size: 10px;
      font-weight: 700;
      color: #7C7C7C;
      line-height: 28px;
    }
  
    .note-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.package-details {
  color: #272728;
  font-size: 14px;
}
.package-details ul{
  padding-right: 20px;
}

.item {
  margin-bottom: 8px;
}

.label {
  font-weight: 700;
}
.value {
  font-weight: 400;
  display: inline;
}
.dot-icon {
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: #272728;
  margin-left: 6px;
}
ul{
  list-style: none;
}
.renewal-note {
  width: 100%;
  display: inline-flex;
  justify-content: flex-start;
  align-items: baseline;
  gap: 6px;
}
  .note-text {
    font-size: 10px;
    font-weight: 700;
    color: #7C7C7C;
    line-height: 28px;
  }

  .note-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }