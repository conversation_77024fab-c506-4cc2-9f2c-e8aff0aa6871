@import "variables";

.header-summary {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 2rem;
}

.header-title {
    display: flex;
    align-items: flex-start;
    gap: 5px;
}

.icon-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
}

::ng-deep .header-title app-svg-icons[name="back-icon"] svg {
    transform: rotate(var(--arrow-rotation-opposite));
}